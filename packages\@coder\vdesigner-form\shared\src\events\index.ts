/**
 * 事件系统
 * 提供统一的事件发布订阅机制
 */

import type { Emitter } from 'mitt';

import mitt from 'mitt';

// ==================== 事件类型定义 ====================

/**
 * 事件数据基类
 */
export interface BaseEventData {
  /** 事件ID */
  eventId?: string;
  /** 事件来源 */
  source?: string;
  /** 事件时间戳 */
  timestamp: number;
}

/**
 * Widget 事件数据
 */
export interface WidgetEventData extends BaseEventData {
  /** 事件相关数据 */
  data?: any;
  /** Widget ID */
  widgetId: string;
  /** Widget 类型 */
  widgetType: string;
}

/**
 * 数据源事件数据
 */
export interface DataSourceEventData extends BaseEventData {
  /** 事件相关数据 */
  data?: any;
  /** 数据源ID */
  dataSourceId: string;
  /** 数据源名称 */
  dataSourceName: string;
  /** 错误信息 */
  error?: Error;
}

/**
 * 渲染器事件数据
 */
export interface RenderEventData extends BaseEventData {
  /** 事件相关数据 */
  data?: any;
  /** 错误信息 */
  error?: Error;
  /** 渲染器ID */
  renderId: string;
}

/**
 * 设计器事件数据
 */
export interface DesignerEventData extends BaseEventData {
  /** 事件相关数据 */
  data?: any;
  /** 设计器ID */
  designerId: string;
}

// ==================== 事件类型映射 ====================

/**
 * 所有事件类型映射
 */
export type EventMap = {
  'datasource:cached': DataSourceEventData;
  // 数据源事件
  'datasource:created': DataSourceEventData;
  'datasource:deleted': DataSourceEventData;
  'datasource:error': DataSourceEventData;
  'datasource:executed': DataSourceEventData;
  'datasource:updated': DataSourceEventData;
  'designer:config:changed': DesignerEventData;

  'designer:mode:changed': DesignerEventData;
  'designer:widget:added': DesignerEventData;
  'designer:widget:copied': DesignerEventData;
  'designer:widget:moved': DesignerEventData;
  'designer:widget:pasted': DesignerEventData;
  'designer:widget:removed': DesignerEventData;

  // 设计器事件
  'designer:widget:selected': DesignerEventData;
  'render:error': RenderEventData;
  // 渲染器事件
  'render:mounted': RenderEventData;
  'render:unmounted': RenderEventData;

  'render:updated': RenderEventData;
  'widget:copied': WidgetEventData;
  // Widget 事件
  'widget:created': WidgetEventData;
  'widget:deleted': WidgetEventData;
  'widget:moved': WidgetEventData;
  'widget:pasted': WidgetEventData;
  'widget:selected': WidgetEventData;
  'widget:updated': WidgetEventData;
};

// ==================== 事件发射器类 ====================

/**
 * 事件发射器接口
 */
export interface IEventEmitter {
  /** 清除所有监听器 */
  clear(): void;
  /** 触发事件 */
  emit<T extends keyof EventMap>(event: T, data: EventMap[T]): void;
  /** 获取事件监听器数量 */
  getListenerCount(event?: keyof EventMap): number;
  /** 取消监听 */
  off<T extends keyof EventMap>(
    event: T,
    handler: (data: EventMap[T]) => void,
  ): void;
  /** 监听事件 */
  on<T extends keyof EventMap>(
    event: T,
    handler: (data: EventMap[T]) => void,
  ): () => void;
  /** 一次性监听 */
  once<T extends keyof EventMap>(
    event: T,
    handler: (data: EventMap[T]) => void,
  ): void;
}

/**
 * 事件发射器实现
 */
export class EventEmitter implements IEventEmitter {
  private emitter: Emitter<EventMap>;

  constructor() {
    this.emitter = mitt<EventMap>();
  }

  clear(): void {
    this.emitter.all.clear();
  }

  emit<T extends keyof EventMap>(event: T, data: EventMap[T]): void {
    this.emitter.emit(event, data);
  }

  getListenerCount(event?: keyof EventMap): number {
    if (event) {
      const handlers = this.emitter.all.get(event);
      return handlers ? handlers.length : 0;
    }
    return [...this.emitter.all.values()].reduce(
      (count, handlers) => count + handlers.length,
      0,
    );
  }

  off<T extends keyof EventMap>(
    event: T,
    handler: (data: EventMap[T]) => void,
  ): void {
    this.emitter.off(event, handler);
  }

  on<T extends keyof EventMap>(
    event: T,
    handler: (data: EventMap[T]) => void,
  ): () => void {
    this.emitter.on(event, handler);
    return () => this.off(event, handler);
  }

  once<T extends keyof EventMap>(
    event: T,
    handler: (data: EventMap[T]) => void,
  ): void {
    const wrappedHandler = (data: EventMap[T]) => {
      handler(data);
      this.off(event, wrappedHandler);
    };
    this.on(event, wrappedHandler);
  }
}

// ==================== 全局事件总线 ====================

/**
 * 全局事件总线
 */
export const globalEventBus = new EventEmitter();

// ==================== 事件工具函数 ====================

/**
 * 创建事件数据
 */
export const createEventData = <T extends BaseEventData>(
  data: Omit<T, 'eventId' | 'timestamp'>,
  source?: string,
): T => {
  return {
    ...data,
    timestamp: Date.now(),
    eventId: `event_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`,
    source,
  } as T;
};

/**
 * 创建 Widget 事件数据
 */
export const createWidgetEventData = (
  widgetId: string,
  widgetType: string,
  data?: any,
  source?: string,
): WidgetEventData => {
  return createEventData<WidgetEventData>(
    {
      widgetId,
      widgetType,
      data,
    },
    source,
  );
};

/**
 * 创建数据源事件数据
 */
export const createDataSourceEventData = (
  dataSourceId: string,
  dataSourceName: string,
  data?: any,
  error?: Error,
  source?: string,
): DataSourceEventData => {
  return createEventData<DataSourceEventData>(
    {
      dataSourceId,
      dataSourceName,
      data,
      error,
    },
    source,
  );
};

/**
 * 创建渲染器事件数据
 */
export const createRenderEventData = (
  renderId: string,
  data?: any,
  error?: Error,
  source?: string,
): RenderEventData => {
  return createEventData<RenderEventData>(
    {
      renderId,
      data,
      error,
    },
    source,
  );
};

/**
 * 创建设计器事件数据
 */
export const createDesignerEventData = (
  designerId: string,
  data?: any,
  source?: string,
): DesignerEventData => {
  return createEventData<DesignerEventData>(
    {
      designerId,
      data,
    },
    source,
  );
};

// ==================== 事件装饰器 ====================

/**
 * 事件监听装饰器
 */
export const EventListener = <T extends keyof EventMap>(event: T) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const result = originalMethod.apply(this, args);

      // 如果类实例有 eventEmitter 属性，则自动注册监听器
      if (this.eventEmitter && typeof this.eventEmitter.on === 'function') {
        this.eventEmitter.on(event, originalMethod.bind(this));
      }

      return result;
    };

    return descriptor;
  };
};

/**
 * 事件发射装饰器
 */
export const EventEmit = <T extends keyof EventMap>(event: T) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const result = originalMethod.apply(this, args);

      // 如果类实例有 eventEmitter 属性，则自动发射事件
      if (this.eventEmitter && typeof this.eventEmitter.emit === 'function') {
        const eventData = args[0]; // 假设第一个参数是事件数据
        this.eventEmitter.emit(event, eventData);
      }

      return result;
    };

    return descriptor;
  };
};

// ==================== 导出 ====================

export { mitt };
export type { Emitter };

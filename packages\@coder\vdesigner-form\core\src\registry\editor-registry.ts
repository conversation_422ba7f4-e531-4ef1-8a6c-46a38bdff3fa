/**
 * 编辑器注册表
 * 提供统一的编辑器注册和管理机制
 */

import type { Component } from 'vue';
import type { 
  EditorRegistry as IEditorRegistry,
  EditorComponentDefinition,
  BaseEditorConfig,
  EditorType,
  EditorBuilder,
  EditorBuilderOptions
} from '../types';

import {
  AbstractFactory,
  EventEmitter
} from '../../../shared/src/base';
import { EDITOR_TYPES } from '../../../shared/src/constants';

// ==================== 编辑器注册表实现 ====================

/**
 * 编辑器注册表实现
 */
export class EditorRegistry extends AbstractFactory<Component, BaseEditorConfig> implements IEditorRegistry {
  /** 编辑器定义映射 */
  private definitions = new Map<EditorType, EditorComponentDefinition>();
  
  /** 事件发射器 */
  private eventEmitter = new EventEmitter();

  constructor() {
    super();
  }

  /**
   * 注册编辑器
   */
  register(definition: EditorComponentDefinition): void {
    // 验证定义
    this.validateDefinition(definition);
    
    // 注册定义
    this.definitions.set(definition.type, definition);
    
    // 注册创建器
    this.registerCreator(definition.type.toString(), (config: BaseEditorConfig) => {
      return definition.component;
    });
    
    this.eventEmitter.emit('editor:registered' as any, {
      type: definition.type,
      definition
    });
  }

  /**
   * 获取编辑器组件
   */
  getEditor(type: EditorType): Component | undefined {
    const definition = this.definitions.get(type);
    return definition?.component;
  }

  /**
   * 获取编辑器定义
   */
  getDefinition(type: EditorType): EditorComponentDefinition | undefined {
    return this.definitions.get(type);
  }

  /**
   * 获取所有编辑器类型
   */
  getAllTypes(): EditorType[] {
    return Array.from(this.definitions.keys());
  }

  /**
   * 创建编辑器配置
   */
  createConfig(type: EditorType, config: Partial<BaseEditorConfig> = {}): BaseEditorConfig {
    const definition = this.definitions.get(type);
    const defaultConfig = definition?.defaultConfig || {};
    
    return {
      type,
      ...defaultConfig,
      ...config
    };
  }

  /**
   * 验证编辑器配置
   */
  validateConfig(type: EditorType, config: BaseEditorConfig): boolean {
    const definition = this.definitions.get(type);
    if (!definition) return false;
    
    if (definition.validateConfig) {
      return definition.validateConfig(config);
    }
    
    return true;
  }

  /**
   * 取消注册编辑器
   */
  unregister(type: EditorType): boolean {
    const removed = this.definitions.delete(type);
    this.creators.delete(type.toString());
    
    if (removed) {
      this.eventEmitter.emit('editor:unregistered' as any, { type });
    }
    
    return removed;
  }

  /**
   * 监听编辑器注册事件
   */
  onEditorRegistered(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('editor:registered' as any, callback);
  }

  /**
   * 监听编辑器取消注册事件
   */
  onEditorUnregistered(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('editor:unregistered' as any, callback);
  }

  /**
   * 验证编辑器定义
   */
  private validateDefinition(definition: EditorComponentDefinition): void {
    if (!definition.type) {
      throw new Error('Editor definition must have a type');
    }
    
    if (!definition.name) {
      throw new Error('Editor definition must have a name');
    }
    
    if (!definition.component) {
      throw new Error('Editor definition must have a component');
    }
  }
}

// ==================== 编辑器构建器实现 ====================

/**
 * 编辑器构建器实现
 */
export class EditorBuilderImpl implements EditorBuilder {
  private registry: EditorRegistry;

  constructor(registry: EditorRegistry) {
    this.registry = registry;
  }

  /**
   * 构建编辑器
   */
  build(options: EditorBuilderOptions): Component {
    const { editorConfig } = options;
    const editor = this.registry.getEditor(editorConfig.type);
    
    if (!editor) {
      throw new Error(`Editor type "${editorConfig.type}" is not registered`);
    }
    
    return editor;
  }

  /**
   * 获取属性值
   */
  getValue(target: any, propertyPath: string): any {
    const keys = propertyPath.split('.');
    let result = target;
    
    for (const key of keys) {
      if (result == null || typeof result !== 'object') {
        return undefined;
      }
      result = result[key];
    }
    
    return result;
  }

  /**
   * 设置属性值
   */
  setValue(target: any, propertyPath: string, value: any): void {
    const keys = propertyPath.split('.');
    const lastKey = keys.pop()!;
    let current = target;
    
    for (const key of keys) {
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[lastKey] = value;
  }

  /**
   * 验证属性值
   */
  validate(target: any, propertyPath: string, config: BaseEditorConfig): boolean {
    const value = this.getValue(target, propertyPath);
    return this.registry.validateConfig(config.type, { ...config, value } as any);
  }
}

// ==================== 编辑器管理器 ====================

/**
 * 编辑器管理器
 * 管理编辑器的创建和配置
 */
export class EditorManager {
  private registry: EditorRegistry;
  private builder: EditorBuilderImpl;
  private eventEmitter = new EventEmitter();

  constructor(registry: EditorRegistry) {
    this.registry = registry;
    this.builder = new EditorBuilderImpl(registry);
  }

  /**
   * 注册编辑器
   */
  registerEditor(definition: EditorComponentDefinition): void {
    this.registry.register(definition);
  }

  /**
   * 创建编辑器实例
   */
  createEditor(type: EditorType, config: Partial<BaseEditorConfig> = {}): Component {
    const editorConfig = this.registry.createConfig(type, config);
    
    return this.builder.build({
      target: {},
      propertyPath: '',
      editorConfig,
      context: {}
    });
  }

  /**
   * 获取编辑器构建器
   */
  getBuilder(): EditorBuilder {
    return this.builder;
  }

  /**
   * 获取注册表
   */
  getRegistry(): EditorRegistry {
    return this.registry;
  }

  /**
   * 获取所有可用的编辑器类型
   */
  getAvailableTypes(): EditorType[] {
    return this.registry.getAllTypes();
  }

  /**
   * 检查编辑器类型是否可用
   */
  isTypeAvailable(type: EditorType): boolean {
    return this.registry.getDefinition(type) !== undefined;
  }

  /**
   * 销毁管理器
   */
  dispose(): void {
    this.eventEmitter.clear();
  }
}

// ==================== 默认实例 ====================

/**
 * 默认编辑器注册表实例
 */
export const defaultEditorRegistry = new EditorRegistry();

/**
 * 默认编辑器管理器实例
 */
export const defaultEditorManager = new EditorManager(defaultEditorRegistry);

/**
 * 默认编辑器构建器实例
 */
export const defaultEditorBuilder = new EditorBuilderImpl(defaultEditorRegistry);

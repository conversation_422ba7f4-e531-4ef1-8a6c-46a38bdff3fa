{"name": "@coder/vdesigner-shared", "version": "0.0.0", "description": "VDesigner Form 共享模块", "private": true, "type": "module", "scripts": {"build": "vite build", "test": "jest"}, "main": "src/index.ts", "module": "src/index.ts", "types": "src/index.ts", "exports": {".": {"types": "./src/index.ts", "import": "./src/index.ts"}}, "dependencies": {"@vueuse/core": "catalog:", "lodash-es": "catalog:coder", "mitt": "catalog:", "nanoid": "catalog:coder", "vue": "catalog:"}, "devDependencies": {"@jest/globals": "catalog:coder", "@types/lodash-es": "catalog:coder", "@vben/tsconfig": "workspace:^", "@vben/vite-config": "workspace:*", "jest": "catalog:coder", "ts-jest": "catalog:coder", "ts-node": "catalog:coder"}}
/**
 * 数据源注册表
 * 提供统一的数据源注册和管理机制
 */

import type { 
  IDataSourceProvider, 
  IDataSourceManager, 
  IDataSource,
  BaseDataSourceConfig,
  DataSourceExecuteParams,
  DataSourceExecuteResult
} from '../types';

import {
  AbstractFactory,
  ObjectCollection,
  EventEmitter
} from '../../../shared/src/base';
import {
  generateDataSourceId
} from '../../../shared/src/utils';
import {
  DATASOURCE_TYPES
} from '../../../shared/src/constants';

// ==================== 数据源注册表实现 ====================

/**
 * 数据源注册表
 */
export class DataSourceRegistry extends AbstractFactory<IDataSource, BaseDataSourceConfig> {
  /** 数据源提供者映射 */
  private providers = new Map<string, IDataSourceProvider>();
  
  /** 事件发射器 */
  private eventEmitter = new EventEmitter();

  constructor() {
    super();
  }

  /**
   * 注册数据源提供者
   */
  registerProvider(provider: IDataSourceProvider): void {
    this.providers.set(provider.type, provider);
    
    // 注册创建器
    this.register(provider.type, (config: BaseDataSourceConfig) => {
      return provider.create(config);
    });
    
    this.eventEmitter.emit('provider:registered' as any, {
      type: provider.type,
      provider
    });
  }

  /**
   * 获取数据源提供者
   */
  getProvider(type: string): IDataSourceProvider | undefined {
    return this.providers.get(type);
  }

  /**
   * 获取所有提供者类型
   */
  getProviderTypes(): string[] {
    return Array.from(this.providers.keys());
  }

  /**
   * 创建数据源实例
   */
  createDataSource(config: BaseDataSourceConfig): IDataSource {
    const provider = this.providers.get(config.type);
    if (!provider) {
      throw new Error(`DataSource provider "${config.type}" is not registered`);
    }

    // 验证配置
    if (!provider.validateConfig(config)) {
      throw new Error(`Invalid configuration for DataSource type "${config.type}"`);
    }

    return provider.create(config);
  }

  /**
   * 验证配置
   */
  validateConfig(config: BaseDataSourceConfig): boolean {
    const provider = this.providers.get(config.type);
    if (!provider) return false;
    
    return provider.validateConfig(config);
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig(type: string): Partial<BaseDataSourceConfig> | undefined {
    const provider = this.providers.get(type);
    return provider?.getDefaultConfig();
  }

  /**
   * 取消注册提供者
   */
  unregisterProvider(type: string): boolean {
    const removed = this.providers.delete(type);
    this.creators.delete(type);
    
    if (removed) {
      this.eventEmitter.emit('provider:unregistered' as any, { type });
    }
    
    return removed;
  }

  /**
   * 监听提供者注册事件
   */
  onProviderRegistered(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('provider:registered' as any, callback);
  }

  /**
   * 监听提供者取消注册事件
   */
  onProviderUnregistered(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('provider:unregistered' as any, callback);
  }
}

// ==================== 数据源管理器实现 ====================

/**
 * 数据源管理器实现
 */
export class DataSourceManager implements IDataSourceManager {
  /** 数据源集合 */
  private dataSources = new ObjectCollection<IDataSource>();
  
  /** 数据源注册表 */
  private registry: DataSourceRegistry;
  
  /** 事件发射器 */
  private eventEmitter = new EventEmitter();

  constructor(registry: DataSourceRegistry) {
    this.registry = registry;
  }

  /**
   * 注册数据源提供者
   */
  registerProvider(provider: IDataSourceProvider): void {
    this.registry.registerProvider(provider);
  }

  /**
   * 创建数据源
   */
  createDataSource(config: BaseDataSourceConfig): IDataSource {
    const dataSource = this.registry.createDataSource({
      ...config,
      id: config.id || generateDataSourceId(config.name)
    });
    
    this.dataSources.add(dataSource);
    this.eventEmitter.emit('datasource:created' as any, { dataSource });
    
    return dataSource;
  }

  /**
   * 获取数据源
   */
  getDataSource(id: string): IDataSource | undefined {
    return this.dataSources.get(id);
  }

  /**
   * 移除数据源
   */
  removeDataSource(id: string): void {
    const dataSource = this.dataSources.get(id);
    if (dataSource) {
      dataSource.destroy();
      this.dataSources.remove(id);
      this.eventEmitter.emit('datasource:removed' as any, { dataSource });
    }
  }

  /**
   * 获取所有数据源
   */
  getAllDataSources(): IDataSource[] {
    return this.dataSources.getAll();
  }

  /**
   * 执行数据源
   */
  async executeDataSource(id: string, params?: DataSourceExecuteParams): Promise<DataSourceExecuteResult> {
    const dataSource = this.dataSources.get(id);
    if (!dataSource) {
      throw new Error(`DataSource with id "${id}" not found`);
    }

    const result = await dataSource.execute(params);
    this.eventEmitter.emit('datasource:executed' as any, { 
      dataSource, 
      result, 
      params 
    });
    
    return result;
  }

  /**
   * 批量执行数据源
   */
  async executeBatch(ids: string[], params?: DataSourceExecuteParams): Promise<DataSourceExecuteResult[]> {
    const promises = ids.map(id => this.executeDataSource(id, params));
    return Promise.all(promises);
  }

  /**
   * 清除所有缓存
   */
  clearAllCache(): void {
    this.dataSources.forEach(dataSource => {
      dataSource.clearCache();
    });
    
    this.eventEmitter.emit('cache:cleared' as any, {});
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.dataSources.forEach(dataSource => {
      dataSource.destroy();
    });
    
    this.dataSources.dispose();
    this.eventEmitter.clear();
  }

  /**
   * 监听数据源创建事件
   */
  onDataSourceCreated(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('datasource:created' as any, callback);
  }

  /**
   * 监听数据源执行事件
   */
  onDataSourceExecuted(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('datasource:executed' as any, callback);
  }
}

// ==================== 默认实例 ====================

/**
 * 默认数据源注册表实例
 */
export const defaultDataSourceRegistry = new DataSourceRegistry();

/**
 * 默认数据源管理器实例
 */
export const defaultDataSourceManager = new DataSourceManager(defaultDataSourceRegistry);

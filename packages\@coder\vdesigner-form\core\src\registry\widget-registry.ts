/**
 * 组件注册表
 * 提供统一的组件注册和管理机制
 */

import type { Component } from 'vue';

import type {
  WidgetRegistry as IWidgetRegistry,
  Widget,
  WidgetCreator,
  WidgetDefinition,
} from '../types';

import {
  AbstractFactory,
  EventEmitter,
  ObjectCollection,
} from '../../../shared/src/base';
import { generateWidgetId } from '../../../shared/src/utils';

// ==================== 组件注册表实现 ====================

/**
 * 组件注册表实现
 */
export class WidgetRegistry
  extends AbstractFactory<Widget, any>
  implements IWidgetRegistry
{
  /** 组件实现映射 */
  private components = new Map<string, Map<string, Component>>();

  /** 默认实现标识 */
  private defaultImplement = 'default';

  /** 组件定义映射 */
  private definitions = new Map<string, WidgetDefinition>();

  /** 事件发射器 */
  private eventEmitter = new EventEmitter();

  constructor() {
    super();
  }

  /**
   * 创建组件实例
   */
  createWidget(type: string, options: Record<string, any> = {}): Widget {
    const definition = this.definitions.get(type);
    if (!definition) {
      throw new Error(`Widget type "${type}" is not registered`);
    }

    return this.createWidgetInstance(definition, options);
  }

  /**
   * 获取所有组件定义
   */
  getAllDefinitions(): WidgetDefinition[] {
    return [...this.definitions.values()];
  }

  /**
   * 获取所有组件类型
   */
  getAllTypes(): string[] {
    return [...this.definitions.keys()];
  }

  /**
   * 获取组件实现
   */
  getComponent(
    type: string,
    implement = this.defaultImplement,
  ): Component | undefined {
    const implementations = this.components.get(type);
    if (!implementations) return undefined;

    return (
      implementations.get(implement) ||
      implementations.get(this.defaultImplement)
    );
  }

  /**
   * 获取默认实现
   */
  getDefaultImplement(): string {
    return this.defaultImplement;
  }

  /**
   * 获取组件定义
   */
  getDefinition(type: string): undefined | WidgetDefinition {
    return this.definitions.get(type);
  }

  /**
   * 获取分组的组件定义
   */
  getDefinitionsByGroup(group: string): WidgetDefinition[] {
    return this.getAllDefinitions().filter((def) => def.group === group);
  }

  /**
   * 检查组件类型是否已注册
   */
  hasType(type: string): boolean {
    return this.definitions.has(type);
  }

  /**
   * 监听注册事件
   */
  onRegistered(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('widget:registered' as any, callback);
  }

  /**
   * 监听取消注册事件
   */
  onUnregistered(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('widget:unregistered' as any, callback);
  }

  /**
   * 注册组件定义
   */
  register(
    definition: WidgetDefinition,
    component: Component,
    implement = 'default',
  ): void {
    // 验证定义
    this.validateDefinition(definition);

    // 注册定义
    this.definitions.set(definition.type, definition);

    // 注册组件实现
    if (!this.components.has(definition.type)) {
      this.components.set(definition.type, new Map());
    }
    this.components.get(definition.type)!.set(implement, component);

    // 注册创建器
    this.registerCreator(definition.type, (options = {}) => {
      return this.createWidgetInstance(definition, options);
    });

    // 发射注册事件
    this.eventEmitter.emit('widget:registered' as any, {
      type: definition.type,
      implement,
      definition,
    });
  }

  /**
   * 设置默认实现
   */
  setDefaultImplement(implement: string): void {
    this.defaultImplement = implement;
  }

  /**
   * 取消注册组件
   */
  unregister(type: string, implement?: string): boolean {
    if (implement) {
      // 取消注册特定实现
      const implementations = this.components.get(type);
      if (implementations) {
        const removed = implementations.delete(implement);
        if (implementations.size === 0) {
          this.components.delete(type);
        }
        return removed;
      }
      return false;
    } else {
      // 取消注册整个类型
      const hasDefinition = this.definitions.delete(type);
      const hasComponents = this.components.delete(type);
      this.creators.delete(type);

      if (hasDefinition) {
        this.eventEmitter.emit('widget:unregistered' as any, { type });
      }

      return hasDefinition || hasComponents;
    }
  }

  /**
   * 创建组件实例
   */
  private createWidgetInstance(
    definition: WidgetDefinition,
    options: Record<string, any>,
  ): Widget {
    const widget: Widget = {
      id: generateWidgetId(definition.type),
      type: definition.type,
      options: {
        ...this.getDefaultOptions(definition),
        ...options,
      },
      widgets: [],
      rules: [],
      dataSourceRef: {},
    };

    // 调用初始化方法
    if (definition.onInit) {
      definition.onInit(widget as any);
    }

    return widget;
  }

  /**
   * 获取默认选项
   */
  private getDefaultOptions(definition: WidgetDefinition): Record<string, any> {
    if (typeof definition.options === 'function') {
      return definition.options();
    }
    return definition.options || {};
  }

  /**
   * 验证组件定义
   */
  private validateDefinition(definition: WidgetDefinition): void {
    if (!definition.type) {
      throw new Error('Widget definition must have a type');
    }

    if (!definition.label) {
      throw new Error('Widget definition must have a label');
    }

    if (!definition.group) {
      throw new Error('Widget definition must have a group');
    }

    if (!definition.mode) {
      throw new Error('Widget definition must have a mode');
    }
  }
}

// ==================== 组件创建器 ====================

/**
 * 组件创建器实现
 */
export class WidgetCreatorImpl implements WidgetCreator {
  private registry: WidgetRegistry;

  constructor(registry: WidgetRegistry) {
    this.registry = registry;
  }

  create(type: string, options: Record<string, any> = {}): Widget {
    return this.registry.createWidget(type, options);
  }

  getTypes(): string[] {
    return this.registry.getAllTypes();
  }

  register(type: string, creator: () => Widget): void {
    // 这里可以注册自定义创建器
    this.registry.registerCreator(type, creator);
  }
}

// ==================== 组件管理器 ====================

/**
 * 组件管理器
 * 管理组件实例的生命周期
 */
export class WidgetManager {
  /** 事件发射器 */
  private eventEmitter = new EventEmitter();

  /** 组件注册表 */
  private registry: WidgetRegistry;

  /** 组件集合 */
  private widgets = new ObjectCollection<Widget>();

  constructor(registry: WidgetRegistry) {
    this.registry = registry;
  }

  /**
   * 清空所有组件
   */
  clear(): void {
    this.widgets.clear();
    this.eventEmitter.emit('widgets:cleared' as any, {});
  }

  /**
   * 创建组件
   */
  createWidget(type: string, options: Record<string, any> = {}): Widget {
    const widget = this.registry.createWidget(type, options);
    this.widgets.add(widget);

    this.eventEmitter.emit('widget:created' as any, { widget });
    return widget;
  }

  /**
   * 销毁管理器
   */
  dispose(): void {
    this.widgets.dispose();
    this.eventEmitter.clear();
  }

  /**
   * 过滤组件
   */
  filterWidgets(predicate: (widget: Widget) => boolean): Widget[] {
    return this.widgets.filter(predicate);
  }

  /**
   * 查找组件
   */
  findWidget(predicate: (widget: Widget) => boolean): undefined | Widget {
    return this.widgets.find(predicate);
  }

  /**
   * 获取所有组件
   */
  getAllWidgets(): Widget[] {
    return this.widgets.getAll();
  }

  /**
   * 获取组件
   */
  getWidget(id: string): undefined | Widget {
    return this.widgets.get(id);
  }

  /**
   * 监听组件创建事件
   */
  onWidgetCreated(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('widget:created' as any, callback);
  }

  /**
   * 监听组件删除事件
   */
  onWidgetRemoved(callback: (data: any) => void): () => void {
    return this.eventEmitter.on('widget:removed' as any, callback);
  }

  /**
   * 删除组件
   */
  removeWidget(id: string): boolean {
    const widget = this.widgets.get(id);
    if (widget) {
      const removed = this.widgets.remove(id);
      if (removed) {
        this.eventEmitter.emit('widget:removed' as any, { widget });
      }
      return removed;
    }
    return false;
  }
}

// ==================== 默认实例 ====================

/**
 * 默认组件注册表实例
 */
export const defaultWidgetRegistry = new WidgetRegistry();

/**
 * 默认组件管理器实例
 */
export const defaultWidgetManager = new WidgetManager(defaultWidgetRegistry);

/**
 * 默认组件创建器实例
 */
export const defaultWidgetCreator = new WidgetCreatorImpl(
  defaultWidgetRegistry,
);

/**
 * 核心类型定义文件
 * 统一管理所有与 VDesigner Form 相关的类型定义
 */

import type { Component } from 'vue';

import type { RequestClient } from '@vben/request';

// ==================== 基础类型 ====================

/**
 * 设备类型
 */
export type DeviceType = 'mobile' | 'pad' | 'pc';

/**
 * 组件分组类型
 */
export enum WidgetGroup {
  Basic = 'basic',
  Customer = 'customer',
  Display = 'display',
  Feedback = 'feedback',
  Form = 'form',
  Layout = 'layout',
  Navigation = 'navigation',
}

/**
 * 组件模式
 */
export type WidgetMode = 'Container' | 'Item' | 'Multi-Container';

// ==================== 数据源相关类型 ====================

/**
 * 数据源引用
 */
export interface IDataSourceRef {
  /** 数据源名称 */
  name: string;
  /** 参数映射 */
  params?: Record<string, any>;
  /** 结果映射路径 */
  resultPath?: string;
}

/**
 * 数据源定义
 */
export interface DataSource {
  /** 是否自动执行 */
  autoExecute?: boolean;
  /** 数据源配置 */
  config: Record<string, any>;
  /** 依赖的其他数据源 */
  dependencies?: string[];
  /** 数据源唯一标识 */
  id: string;
  /** 数据源名称 */
  name: string;
  /** 数据源类型 */
  type: 'ajax' | 'code' | 'keyMap' | 'static';
}

// ==================== 组件相关类型 ====================

/**
 * 基础验证规则
 */
export interface BaseRule {
  /** 错误消息 */
  message?: string;
  /** 是否必填 */
  required?: boolean;
  /** 触发方式 */
  trigger?: string | string[];
  /** 规则类型 */
  type: string;
  /** 自定义验证函数 */
  validator?: (rule: any, value: any, callback: any) => void;
}

/**
 * 默认编辑器选项
 */
export interface DefaultEditoOptions {
  /** 自定义CSS类 */
  customClass?: string[];
  /** 是否隐藏 */
  hidden?: boolean;
  /** 移动端宽度 */
  mobileWidth?: string;
  /** 平板端宽度 */
  padWidth?: string;
  /** PC端宽度 */
  pcWidth?: string;
}

/**
 * Widget 组件定义
 */
export interface Widget {
  /** 数据源引用 */
  dataSourceRef: Record<string, IDataSourceRef>;
  /** 组件唯一标识 */
  id: string;
  /** 组件选项配置 */
  options: DefaultEditoOptions & Record<string, any>;
  /** 验证规则 */
  rules?: BaseRule[];
  /** 序列化方法 */
  toJSON?(): string;
  /** 组件类型 */
  type: string;
  /** 子组件列表 */
  widgets: Widget[];
}

// ==================== 渲染相关类型 ====================

/**
 * 渲染配置
 * 这是核心的数据结构，不能改变其基本结构
 */
export interface RenderConfig {
  /** 数据源列表 */
  dataSource?: DataSource[];
  /** 根组件 */
  rootWidget?: Widget;
}

/**
 * 渲染属性类型
 */
export interface RenderPropsType {
  /** 全局配置 */
  cfg?: Record<string, any>;
  /** 表单数据 */
  formData?: Record<string, any>;
  /** 实现标识 */
  implement?: string;
  /** 是否设计模式 */
  isDesign?: boolean;
  /** 渲染配置 */
  renderConfig?: RenderConfig;
  /** 渲染器ID */
  renderId?: string;
}

/**
 * 渲染模型
 */
export interface RenderModel {
  /** 数据源列表 */
  dataSource: DataSource[];
  /** 表单数据 */
  formData: any;
  /** 实现方式 */
  implement: string;
  /** 是否设计模式 */
  isDesign?: boolean;
  /** 根组件 */
  rootWidget: Widget;
  /** 组件映射表 */
  widgets: Record<string, Widget>;
}

// ==================== 组件定义相关类型 ====================

/**
 * 组件选项编辑器设置
 */
export interface WidgetOptionEditorSetting {
  /** 编辑器配置 */
  config?: Record<string, any>;
  /** 默认值 */
  defaultValue?: any;
  /** 是否必填 */
  required?: boolean;
  /** 编辑器类型 */
  type: string;
}

/**
 * 组件定义
 */
export interface WidgetDefinition {
  /** 创建组件实例的方法 */
  createWidget?(): Widget;
  /** 组件描述 */
  description?: string;
  /** 组件分组 */
  group: WidgetGroup;
  /** 自定义分组标签 */
  groupLabel?: string;
  /** 组件图标 */
  icon: Component | string;
  /** 代码提示 */
  intelliSense?: string;
  /** 组件显示名称 */
  label: string;
  /** 组件模式 */
  mode: WidgetMode;
  /** 初始化方法 */
  onInit?: (model: any) => void;
  /** 选项编辑器配置 */
  optionEditors?: Record<string, false | WidgetOptionEditorSetting>;
  /** 默认选项生成器 */
  options?: () => Record<string, any>;
  /** 排序权重 */
  order?: number;
  /** 组件类型标识 */
  type: string;
}

// ==================== 配置相关类型 ====================

/**
 * 渲染器选项
 */
export interface RenderOptions {
  /** 默认实现标识 */
  defaultImple?: string;
  /** 请求客户端 */
  request: RequestClient;
  /** 存储主机地址 */
  storageHost?: string;
}

/**
 * 设计器属性类型
 */
export interface DesignerPropsType {
  /** 是否只读模式 */
  readonly?: boolean;
  /** 渲染配置 */
  renderConfig?: RenderConfig;
  /** 渲染器ID */
  renderId?: string;
  /** 自定义样式 */
  style?: any;
}

// ==================== 事件相关类型 ====================

/**
 * 事件发射器类型
 */
export interface EventEmitter {
  /** 触发事件 */
  emit(event: string, ...args: any[]): void;
  /** 取消监听 */
  off(event: string, handler: Function): void;
  /** 监听事件 */
  on(event: string, handler: Function): void;
  /** 一次性监听 */
  once(event: string, handler: Function): void;
}

// ==================== 导出所有类型 ====================

export * from './datasource-types';
export * from './editor-types';
export * from './widget-types';

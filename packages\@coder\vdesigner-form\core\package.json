{"name": "@coder/vdesigner-core", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "vite build", "test": "jest"}, "main": "src/index.ts", "typings": "src/index.ts", "dependencies": {"@ant-design/icons-vue": "catalog:coder", "@coder/string-format": "workspace:*", "@coder/vdesigner-shared": "workspace:*", "@vben/request": "workspace:^", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "lodash-es": "catalog:coder", "mitt": "catalog:", "nanoid": "catalog:coder", "pinia": "catalog:", "vue": "catalog:"}, "devDependencies": {"@jest/globals": "catalog:coder", "@types/lodash-es": "catalog:coder", "@vben/tsconfig": "workspace:^", "@vben/vite-config": "workspace:*", "jest": "catalog:coder", "ts-jest": "catalog:coder", "ts-node": "catalog:coder"}}
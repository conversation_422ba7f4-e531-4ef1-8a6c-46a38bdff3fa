/**
 * 设计器核心
 * 提供设计器的核心功能和状态管理
 */

import type { 
  RenderConfig, 
  Widget, 
  DataSource,
  DesignerPropsType,
  WidgetDefinition
} from '../../types';

import {
  BaseObject,
  EventEmitter
} from '../../../../shared/src/base';
import {
  deepClone,
  generateId
} from '../../../../shared/src/utils';
import {
  DESIGNER_EVENTS
} from '../../../../shared/src/constants';

import { defaultWidgetRegistry } from '../../registry';
import { RenderEngine } from '../../render/engine';

// ==================== 设计器状态 ====================

/**
 * 设计器状态
 */
export interface DesignerState {
  /** 当前选中的组件ID */
  selectedWidgetId?: string;
  /** 当前悬停的组件ID */
  hoveredWidgetId?: string;
  /** 是否处于拖拽状态 */
  isDragging: boolean;
  /** 拖拽的组件类型 */
  draggingWidgetType?: string;
  /** 是否只读模式 */
  readonly: boolean;
  /** 网格大小 */
  gridSize: number;
  /** 是否显示网格 */
  showGrid: boolean;
  /** 是否启用吸附 */
  snapToGrid: boolean;
  /** 缩放比例 */
  scale: number;
}

/**
 * 设计器操作历史项
 */
export interface DesignerHistoryItem {
  /** 操作ID */
  id: string;
  /** 操作类型 */
  type: 'add' | 'remove' | 'update' | 'move' | 'copy' | 'paste';
  /** 操作描述 */
  description: string;
  /** 操作前的配置 */
  beforeConfig: RenderConfig;
  /** 操作后的配置 */
  afterConfig: RenderConfig;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 设计器配置
 */
export interface DesignerConfig {
  /** 最大历史记录数 */
  maxHistorySize?: number;
  /** 是否启用自动保存 */
  autoSave?: boolean;
  /** 自动保存间隔（毫秒） */
  autoSaveInterval?: number;
  /** 网格大小 */
  gridSize?: number;
  /** 是否启用吸附 */
  snapToGrid?: boolean;
}

// ==================== 设计器核心实现 ====================

/**
 * 设计器核心实现
 */
export class DesignerCore extends BaseObject {
  /** 渲染配置 */
  private renderConfig: RenderConfig;
  
  /** 设计器状态 */
  private state: DesignerState;
  
  /** 设计器配置 */
  private config: Required<DesignerConfig>;
  
  /** 操作历史 */
  private history: DesignerHistoryItem[] = [];
  
  /** 当前历史索引 */
  private historyIndex = -1;
  
  /** 渲染引擎 */
  private renderEngine?: RenderEngine;
  
  /** 自动保存定时器 */
  private autoSaveTimer?: NodeJS.Timeout;

  constructor(
    designerId: string,
    initialConfig: RenderConfig = {},
    config: DesignerConfig = {}
  ) {
    super(designerId, `Designer_${designerId}`);
    
    this.renderConfig = {
      dataSource: [],
      rootWidget: undefined,
      ...initialConfig
    };
    
    this.state = {
      isDragging: false,
      readonly: false,
      gridSize: 8,
      showGrid: true,
      snapToGrid: true,
      scale: 1
    };
    
    this.config = {
      maxHistorySize: 50,
      autoSave: true,
      autoSaveInterval: 30000, // 30秒
      gridSize: 8,
      snapToGrid: true,
      ...config
    };
    
    // 初始化状态
    this.state.gridSize = this.config.gridSize;
    this.state.snapToGrid = this.config.snapToGrid;
    
    // 启动自动保存
    if (this.config.autoSave) {
      this.startAutoSave();
    }
  }

  /**
   * 初始化设计器
   */
  async initialize(props: DesignerPropsType): Promise<void> {
    try {
      // 更新配置
      if (props.renderConfig) {
        this.renderConfig = { ...props.renderConfig };
      }
      
      // 更新状态
      this.state.readonly = props.readonly || false;
      
      // 创建渲染引擎
      this.renderEngine = new RenderEngine(this.id, {
        renderId: this.id,
        isDesign: true,
        implement: 'default',
        cfg: {},
        formData: {}
      });
      
      // 初始化渲染引擎
      await this.renderEngine.initialize({
        renderConfig: this.renderConfig,
        isDesign: true
      });
      
      // 发射初始化完成事件
      this.eventEmitter.emit('designer:initialized' as any, {
        designerId: this.id,
        config: this.renderConfig,
        timestamp: Date.now()
      });
      
    } catch (error) {
      this.eventEmitter.emit('designer:error' as any, {
        designerId: this.id,
        error,
        timestamp: Date.now()
      });
      throw error;
    }
  }

  /**
   * 添加组件
   */
  addWidget(
    widgetType: string, 
    parentId?: string, 
    index?: number,
    options?: Record<string, any>
  ): Widget {
    if (this.state.readonly) {
      throw new Error('Designer is in readonly mode');
    }
    
    // 创建组件
    const widget = defaultWidgetRegistry.createWidget(widgetType, options);
    
    // 记录操作前状态
    const beforeConfig = deepClone(this.renderConfig);
    
    // 添加到父组件或根组件
    if (parentId) {
      const parentWidget = this.findWidget(parentId);
      if (!parentWidget) {
        throw new Error(`Parent widget with id "${parentId}" not found`);
      }
      
      if (index !== undefined) {
        parentWidget.widgets.splice(index, 0, widget);
      } else {
        parentWidget.widgets.push(widget);
      }
    } else {
      // 添加为根组件
      if (!this.renderConfig.rootWidget) {
        this.renderConfig.rootWidget = widget;
      } else {
        throw new Error('Root widget already exists');
      }
    }
    
    // 记录操作历史
    this.addToHistory('add', `添加组件 ${widgetType}`, beforeConfig, deepClone(this.renderConfig));
    
    // 选中新添加的组件
    this.selectWidget(widget.id);
    
    // 发射事件
    this.eventEmitter.emit('designer:widget:added' as any, {
      designerId: this.id,
      widget,
      parentId,
      index,
      timestamp: Date.now()
    });
    
    return widget;
  }

  /**
   * 移除组件
   */
  removeWidget(widgetId: string): boolean {
    if (this.state.readonly) {
      throw new Error('Designer is in readonly mode');
    }
    
    const widget = this.findWidget(widgetId);
    if (!widget) {
      return false;
    }
    
    // 记录操作前状态
    const beforeConfig = deepClone(this.renderConfig);
    
    // 移除组件
    const removed = this.removeWidgetFromTree(widgetId);
    
    if (removed) {
      // 记录操作历史
      this.addToHistory('remove', `移除组件 ${widget.type}`, beforeConfig, deepClone(this.renderConfig));
      
      // 清除选中状态
      if (this.state.selectedWidgetId === widgetId) {
        this.selectWidget(undefined);
      }
      
      // 发射事件
      this.eventEmitter.emit('designer:widget:removed' as any, {
        designerId: this.id,
        widget,
        timestamp: Date.now()
      });
    }
    
    return removed;
  }

  /**
   * 更新组件
   */
  updateWidget(widgetId: string, updates: Partial<Widget>): boolean {
    if (this.state.readonly) {
      throw new Error('Designer is in readonly mode');
    }
    
    const widget = this.findWidget(widgetId);
    if (!widget) {
      return false;
    }
    
    // 记录操作前状态
    const beforeConfig = deepClone(this.renderConfig);
    
    // 更新组件
    Object.assign(widget, updates);
    
    // 记录操作历史
    this.addToHistory('update', `更新组件 ${widget.type}`, beforeConfig, deepClone(this.renderConfig));
    
    // 发射事件
    this.eventEmitter.emit('designer:widget:updated' as any, {
      designerId: this.id,
      widget,
      updates,
      timestamp: Date.now()
    });
    
    return true;
  }

  /**
   * 移动组件
   */
  moveWidget(widgetId: string, newParentId: string, newIndex?: number): boolean {
    if (this.state.readonly) {
      throw new Error('Designer is in readonly mode');
    }
    
    const widget = this.findWidget(widgetId);
    const newParent = this.findWidget(newParentId);
    
    if (!widget || !newParent) {
      return false;
    }
    
    // 记录操作前状态
    const beforeConfig = deepClone(this.renderConfig);
    
    // 从原位置移除
    this.removeWidgetFromTree(widgetId);
    
    // 添加到新位置
    if (newIndex !== undefined) {
      newParent.widgets.splice(newIndex, 0, widget);
    } else {
      newParent.widgets.push(widget);
    }
    
    // 记录操作历史
    this.addToHistory('move', `移动组件 ${widget.type}`, beforeConfig, deepClone(this.renderConfig));
    
    // 发射事件
    this.eventEmitter.emit('designer:widget:moved' as any, {
      designerId: this.id,
      widget,
      newParentId,
      newIndex,
      timestamp: Date.now()
    });
    
    return true;
  }

  /**
   * 选中组件
   */
  selectWidget(widgetId?: string): void {
    const previousId = this.state.selectedWidgetId;
    this.state.selectedWidgetId = widgetId;
    
    this.eventEmitter.emit('designer:widget:selected' as any, {
      designerId: this.id,
      widgetId,
      previousId,
      timestamp: Date.now()
    });
  }

  /**
   * 悬停组件
   */
  hoverWidget(widgetId?: string): void {
    this.state.hoveredWidgetId = widgetId;
    
    this.eventEmitter.emit('designer:widget:hovered' as any, {
      designerId: this.id,
      widgetId,
      timestamp: Date.now()
    });
  }

  /**
   * 撤销操作
   */
  undo(): boolean {
    if (this.state.readonly || this.historyIndex <= 0) {
      return false;
    }
    
    this.historyIndex--;
    const historyItem = this.history[this.historyIndex];
    this.renderConfig = deepClone(historyItem.beforeConfig);
    
    this.eventEmitter.emit('designer:undo' as any, {
      designerId: this.id,
      historyItem,
      timestamp: Date.now()
    });
    
    return true;
  }

  /**
   * 重做操作
   */
  redo(): boolean {
    if (this.state.readonly || this.historyIndex >= this.history.length - 1) {
      return false;
    }
    
    this.historyIndex++;
    const historyItem = this.history[this.historyIndex];
    this.renderConfig = deepClone(historyItem.afterConfig);
    
    this.eventEmitter.emit('designer:redo' as any, {
      designerId: this.id,
      historyItem,
      timestamp: Date.now()
    });
    
    return true;
  }

  /**
   * 获取渲染配置
   */
  getRenderConfig(): RenderConfig {
    return deepClone(this.renderConfig);
  }

  /**
   * 设置渲染配置
   */
  setRenderConfig(config: RenderConfig): void {
    const beforeConfig = deepClone(this.renderConfig);
    this.renderConfig = deepClone(config);
    
    this.addToHistory('update', '更新配置', beforeConfig, deepClone(this.renderConfig));
    
    this.eventEmitter.emit('designer:config:changed' as any, {
      designerId: this.id,
      config: this.renderConfig,
      timestamp: Date.now()
    });
  }

  /**
   * 获取设计器状态
   */
  getState(): DesignerState {
    return { ...this.state };
  }

  /**
   * 更新设计器状态
   */
  updateState(updates: Partial<DesignerState>): void {
    const previousState = { ...this.state };
    Object.assign(this.state, updates);
    
    this.eventEmitter.emit('designer:state:changed' as any, {
      designerId: this.id,
      state: this.state,
      previousState,
      timestamp: Date.now()
    });
  }

  /**
   * 查找组件
   */
  findWidget(widgetId: string): Widget | undefined {
    if (!this.renderConfig.rootWidget) {
      return undefined;
    }
    
    const traverse = (widget: Widget): Widget | undefined => {
      if (widget.id === widgetId) {
        return widget;
      }
      
      for (const child of widget.widgets || []) {
        const found = traverse(child);
        if (found) {
          return found;
        }
      }
      
      return undefined;
    };
    
    return traverse(this.renderConfig.rootWidget);
  }

  /**
   * 获取操作历史
   */
  getHistory(): DesignerHistoryItem[] {
    return [...this.history];
  }

  /**
   * 清除历史
   */
  clearHistory(): void {
    this.history = [];
    this.historyIndex = -1;
  }

  /**
   * 销毁设计器
   */
  dispose(): void {
    if (this.disposed) return;
    
    // 停止自动保存
    this.stopAutoSave();
    
    // 销毁渲染引擎
    if (this.renderEngine) {
      this.renderEngine.dispose();
    }
    
    // 清除历史
    this.clearHistory();
    
    super.dispose();
  }

  /**
   * 从组件树中移除组件
   */
  private removeWidgetFromTree(widgetId: string): boolean {
    if (!this.renderConfig.rootWidget) {
      return false;
    }
    
    if (this.renderConfig.rootWidget.id === widgetId) {
      this.renderConfig.rootWidget = undefined;
      return true;
    }
    
    const traverse = (widget: Widget): boolean => {
      if (!widget.widgets) {
        return false;
      }
      
      const index = widget.widgets.findIndex(child => child.id === widgetId);
      if (index !== -1) {
        widget.widgets.splice(index, 1);
        return true;
      }
      
      for (const child of widget.widgets) {
        if (traverse(child)) {
          return true;
        }
      }
      
      return false;
    };
    
    return traverse(this.renderConfig.rootWidget);
  }

  /**
   * 添加到操作历史
   */
  private addToHistory(
    type: DesignerHistoryItem['type'],
    description: string,
    beforeConfig: RenderConfig,
    afterConfig: RenderConfig
  ): void {
    const historyItem: DesignerHistoryItem = {
      id: generateId(),
      type,
      description,
      beforeConfig,
      afterConfig,
      timestamp: Date.now()
    };
    
    // 移除当前索引之后的历史
    this.history = this.history.slice(0, this.historyIndex + 1);
    
    // 添加新的历史项
    this.history.push(historyItem);
    this.historyIndex = this.history.length - 1;
    
    // 限制历史大小
    if (this.history.length > this.config.maxHistorySize) {
      this.history.shift();
      this.historyIndex--;
    }
  }

  /**
   * 启动自动保存
   */
  private startAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }
    
    this.autoSaveTimer = setInterval(() => {
      this.eventEmitter.emit('designer:auto:save' as any, {
        designerId: this.id,
        config: this.renderConfig,
        timestamp: Date.now()
      });
    }, this.config.autoSaveInterval);
  }

  /**
   * 停止自动保存
   */
  private stopAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = undefined;
    }
  }
}

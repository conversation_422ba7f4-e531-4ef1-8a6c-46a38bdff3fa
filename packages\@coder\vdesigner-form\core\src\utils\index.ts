import type { DataSource } from '../dataSource';
import type { Widget } from '../widgets';

import { PlaceHolder, templateFormat } from '@coder/string-format';
import {
  generateId as sharedGenerateId,
  isPromise as sharedIsPromise
} from '../../../shared/src/utils';

export * from './getRuleTypeLabel';
export * from './propertyAccessor';

// 重新导出共享工具函数以保持向后兼容
export const isPromise = sharedIsPromise;
export const generateId = sharedGenerateId;
/**
 * 序列化
 * @param rootWidget
 * @param dataSource
 * @returns string
 */
export const toJson = (rootWidget: Widget, dataSource: DataSource[]) => {
  return JSON.stringify({ dataSource, rootWidget }, (key, val) => {
    if (key.startsWith('$')) return;
    return val;
  });
};
/**
 *
 * @param template 字符串模板
 * @param data formData
 * @param cfg cfg配置
 * @returns
 */
export const formatFromTemplate = (
  template: string,
  data: Record<string, any>,
  cfg: Record<string, any>,
) => {
  const url = templateFormat(template, cfg, {}, PlaceHolder._);
  const t = templateFormat(url, data, {}, PlaceHolder.$);
  return t;
};
